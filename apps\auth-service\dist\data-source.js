"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./src/auth/user.entity");
const refresh_token_entity_1 = require("./src/auth/refresh-token.entity");
const dotenv = require("dotenv");
dotenv.config({ path: ['.env.local', '.env'] });
exports.AppDataSource = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.AUTH_DB_HOST || 'localhost',
    port: parseInt(process.env.AUTH_DB_PORT || '5433'),
    username: process.env.AUTH_DB_USERNAME || 'authuser',
    password: process.env.AUTH_DB_PASSWORD || 'authpassword',
    database: process.env.AUTH_DB_DATABASE || 'authdb',
    entities: [user_entity_1.User, refresh_token_entity_1.RefreshToken],
    migrations: ['src/migration/*.ts'],
    synchronize: false,
    logging: true,
});
exports.default = exports.AppDataSource;
//# sourceMappingURL=data-source.js.map