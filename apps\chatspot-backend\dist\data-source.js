"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./src/auth/user.entity");
const refresh_token_entity_1 = require("./src/auth/refresh-token.entity");
const message_entity_1 = require("./src/chat/message.entity");
const fcm_token_entity_1 = require("./src/notifications/entities/fcm-token.entity");
const isProduction = process.env.NODE_ENV === 'production';
const AppDataSource = new typeorm_1.DataSource({
    type: 'postgres',
    url: process.env.DATABASE_URL,
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
    username: process.env.DB_USERNAME || 'chatuser',
    password: process.env.DB_PASSWORD || 'chatpassword',
    database: process.env.DB_DATABASE || 'chatdb',
    synchronize: false,
    ssl: isProduction ? { rejectUnauthorized: false } : false,
    logging: true,
    entities: [user_entity_1.User, refresh_token_entity_1.RefreshToken, message_entity_1.Message, fcm_token_entity_1.FcmToken],
    migrations: ['src/migration/**/*.ts'],
    migrationsTransactionMode: 'each',
});
exports.default = AppDataSource;
//# sourceMappingURL=data-source.js.map