import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateMessageDto {
  @ApiProperty({
    description: 'Username of the sender',
    example: 'johndoe',
  })
  @IsString()
  @IsNotEmpty()
  sender_username: string;

  @ApiProperty({
    description: 'Username of the receiver',
    example: 'janedoe',
  })
  @IsString()
  @IsNotEmpty()
  receiver_username: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'clear_chat', 'typing', 'delete_user', 'system'],
    default: 'text',
  })
  @IsString()
  @IsOptional()
  type?: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';

  @ApiProperty({
    description: 'Client-generated message ID for tracking',
    example: 'client-msg-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  client_message_id?: string;
}
