"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./user.entity");
const bcrypt = require("bcrypt");
const jwt_1 = require("@nestjs/jwt");
const refresh_token_service_1 = require("./refresh-token.service");
let AuthService = class AuthService {
    constructor(userRepo, jwtService, refreshTokenService) {
        this.userRepo = userRepo;
        this.jwtService = jwtService;
        this.refreshTokenService = refreshTokenService;
    }
    async register(username, password) {
        if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
            throw new common_1.BadRequestException('Invalid username format');
        }
        if (!/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password)) {
            throw new common_1.BadRequestException('Password too weak');
        }
        const existingUser = await this.userRepo.findOne({ where: { username } });
        if (existingUser) {
            throw new common_1.ConflictException('Username already exists');
        }
        const hashed = await bcrypt.hash(password, 10);
        const user = this.userRepo.create({ username, password: hashed });
        const savedUser = await this.userRepo.save(user);
        const payload = {
            sub: savedUser.id,
            username: savedUser.username,
            isAdmin: savedUser.isAdmin
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = await this.refreshTokenService.generateRefreshToken(savedUser.id);
        return {
            user: savedUser,
            access_token: accessToken,
            refresh_token: refreshToken.token,
        };
    }
    async login(username, password, deviceInfo) {
        const user = await this.userRepo.findOne({ where: { username } });
        if (!user)
            throw new common_1.UnauthorizedException('Invalid credentials');
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch)
            throw new common_1.UnauthorizedException('Invalid credentials');
        await this.refreshTokenService.revokeAllUserTokens(user.id);
        const payload = {
            sub: user.id,
            username: user.username,
            isAdmin: user.isAdmin
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = await this.refreshTokenService.generateRefreshToken(user.id, deviceInfo);
        return {
            access_token: accessToken,
            refresh_token: refreshToken.token,
            user: {
                id: user.id,
                username: user.username,
                isAdmin: user.isAdmin
            }
        };
    }
    async refreshAccessToken(refreshTokenString) {
        const refreshToken = await this.refreshTokenService.validateRefreshToken(refreshTokenString);
        if (!refreshToken) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
        const payload = {
            sub: refreshToken.user.id,
            username: refreshToken.user.username,
            isAdmin: refreshToken.user.isAdmin
        };
        const accessToken = this.jwtService.sign(payload);
        const newRefreshToken = await this.refreshTokenService.rotateRefreshToken(refreshTokenString, refreshToken.user.id);
        return {
            access_token: accessToken,
            refresh_token: newRefreshToken.token,
        };
    }
    async logout(refreshTokenString) {
        await this.refreshTokenService.revokeRefreshToken(refreshTokenString);
    }
    async logoutAll(userId) {
        await this.refreshTokenService.revokeAllUserTokens(userId);
    }
    async validateSession(userId) {
        return this.refreshTokenService.hasValidTokens(userId);
    }
    async getUserById(userId) {
        return this.userRepo.findOne({ where: { id: userId } });
    }
    async getUserByUsername(username) {
        return this.userRepo.findOne({ where: { username } });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService,
        refresh_token_service_1.RefreshTokenService])
], AuthService);
//# sourceMappingURL=auth.service.js.map