import { Repository } from 'typeorm';
import { User } from './user.entity';
import { UserDto } from './dto';
export declare class UsersService {
    private userRepo;
    constructor(userRepo: Repository<User>);
    findUserIdByUsername(username: string): Promise<string>;
    findUsernameByUserId(userId: string): Promise<string>;
    findUserById(userId: string): Promise<UserDto>;
    getAllUsers(): Promise<UserDto[]>;
    createUser(username: string, hashedPassword: string, isAdmin?: boolean): Promise<UserDto>;
    updateUser(userId: string, updates: Partial<{
        username: string;
        password: string;
        isAdmin: boolean;
    }>): Promise<UserDto>;
    deleteUser(userId: string): Promise<void>;
}
