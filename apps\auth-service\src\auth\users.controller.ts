import { <PERSON>, Get, Param, NotFoundException, UseGuards, Post, Body, Put, Delete } from '@nestjs/common';
import { UsersService } from './users.service';
import { UserDto } from './dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from './jwt-auth.guard';
import * as bcrypt from 'bcrypt';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('resolve/:username')
  @ApiOperation({ summary: 'Resolve username to userId' })
  @ApiParam({ name: 'username', description: 'Username to resolve', example: 'johndoe' })
  @ApiResponse({
    status: 200,
    description: 'Returns the userId for the given username',
    schema: {
      type: 'object',
      properties: {
        userId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
          description: 'The user ID corresponding to the username',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async resolveUsername(@Param('username') username: string): Promise<{ userId: string }> {
    try {
      const userId = await this.usersService.findUserIdByUsername(username);
      return { userId };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('Error resolving username');
    }
  }

  @Get(':userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get user information by userId' })
  @ApiParam({ name: 'userId', description: 'User ID to fetch', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: 200,
    description: 'Returns the user information',
    type: UserDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('userId') userId: string): Promise<UserDto> {
    return this.usersService.findUserById(userId);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get all users (for admin/internal use)' })
  @ApiResponse({
    status: 200,
    description: 'Returns all users',
    type: [UserDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getAllUsers(): Promise<UserDto[]> {
    return this.usersService.getAllUsers();
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new user (admin only)' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string', example: 'newuser' },
        password: { type: 'string', example: 'StrongP@ss123' },
        isAdmin: { type: 'boolean', example: false, default: false }
      },
      required: ['username', 'password']
    }
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createUser(@Body() createUserDto: { username: string; password: string; isAdmin?: boolean }): Promise<UserDto> {
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    return this.usersService.createUser(createUserDto.username, hashedPassword, createUserDto.isAdmin || false);
  }

  @Put(':userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update user information (admin only)' })
  @ApiParam({ name: 'userId', description: 'User ID to update' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string', example: 'updateduser' },
        password: { type: 'string', example: 'NewStrongP@ss123' },
        isAdmin: { type: 'boolean', example: true }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUser(
    @Param('userId') userId: string,
    @Body() updateUserDto: { username?: string; password?: string; isAdmin?: boolean }
  ): Promise<UserDto> {
    const updates: any = { ...updateUserDto };
    if (updates.password) {
      updates.password = await bcrypt.hash(updates.password, 10);
    }
    return this.usersService.updateUser(userId, updates);
  }

  @Delete(':userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Delete a user (admin only)' })
  @ApiParam({ name: 'userId', description: 'User ID to delete' })
  @ApiResponse({
    status: 200,
    description: 'User deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'User deleted successfully' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async deleteUser(@Param('userId') userId: string): Promise<{ message: string }> {
    await this.usersService.deleteUser(userId);
    return { message: 'User deleted successfully' };
  }
}
