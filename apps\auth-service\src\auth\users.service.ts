import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';
import { UserDto } from './dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  /**
   * Find a user by their username
   * @param username The username to search for
   * @returns The user's ID if found
   * @throws NotFoundException if the user is not found
   */
  async findUserIdByUsername(username: string): Promise<string> {
    const user = await this.userRepo.findOne({ where: { username } });
    if (!user) {
      throw new NotFoundException(`User with username "${username}" not found`);
    }
    return user.id;
  }

  /**
   * Find a username by their user ID
   * @param userId The user ID to search for
   * @returns The username if found
   * @throws NotFoundException if the user is not found
   */
  async findUsernameByUserId(userId: string): Promise<string> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }
    return user.username;
  }

  /**
   * Find a user by their ID
   * @param userId The user ID to search for
   * @returns The user information if found
   * @throws NotFoundException if the user is not found
   */
  async findUserById(userId: string): Promise<UserDto> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // Convert to DTO to avoid exposing sensitive information
    const userDto: UserDto = {
      id: user.id,
      username: user.username,
      isAdmin: user.isAdmin,
    };

    return userDto;
  }

  /**
   * Get all users (for admin purposes)
   */
  async getAllUsers(): Promise<UserDto[]> {
    const users = await this.userRepo.find();
    return users.map(user => ({
      id: user.id,
      username: user.username,
      isAdmin: user.isAdmin,
    }));
  }

  /**
   * Create a new user (for admin purposes)
   */
  async createUser(username: string, hashedPassword: string, isAdmin: boolean = false): Promise<UserDto> {
    const user = this.userRepo.create({
      username,
      password: hashedPassword,
      isAdmin,
    });

    const savedUser = await this.userRepo.save(user);
    
    return {
      id: savedUser.id,
      username: savedUser.username,
      isAdmin: savedUser.isAdmin,
    };
  }

  /**
   * Update user information (for admin purposes)
   */
  async updateUser(userId: string, updates: Partial<{ username: string; password: string; isAdmin: boolean }>): Promise<UserDto> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    Object.assign(user, updates);
    const updatedUser = await this.userRepo.save(user);

    return {
      id: updatedUser.id,
      username: updatedUser.username,
      isAdmin: updatedUser.isAdmin,
    };
  }

  /**
   * Delete a user (for admin purposes)
   */
  async deleteUser(userId: string): Promise<void> {
    const result = await this.userRepo.delete(userId);
    if (result.affected === 0) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }
  }
}
