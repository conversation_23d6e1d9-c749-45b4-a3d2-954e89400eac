{"name": "mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "android:localhost": "adb reverse tcp:3002 tcp:3002 && react-native run-android", "android:reactotron": "adb reverse tcp:9090 tcp:9090 && echo \"Please make sure you reload the app and Reactotron App is running\"", "ios": "react-native run-ios", "ios:localhost": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "start:localhost": "react-native start --reset-cache", "test": "jest"}, "dependencies": {"@nozbe/watermelondb": "^0.27.1", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native/gradle-plugin": "^0.79.3", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.7", "react": "19.0.0", "react-native": "0.79.2", "react-native-bootsplash": "^6.3.9", "react-native-gesture-handler": "^2.25.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.1.0", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "rxjs": "^7.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-make": "^1.0.1", "react-test-renderer": "19.0.0", "reactotron-react-native": "^5.1.13", "reactotron-redux": "^3.2.0", "reactotron-redux-saga": "^4.2.3", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}