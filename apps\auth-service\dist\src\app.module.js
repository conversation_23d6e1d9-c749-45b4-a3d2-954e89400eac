"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const auth_module_1 = require("./auth/auth.module");
const user_entity_1 = require("./auth/user.entity");
const refresh_token_entity_1 = require("./auth/refresh-token.entity");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => {
                    const databaseUrl = configService.get('AUTH_DATABASE_URL');
                    if (databaseUrl) {
                        return {
                            type: 'postgres',
                            url: databaseUrl,
                            entities: [user_entity_1.User, refresh_token_entity_1.RefreshToken],
                            synchronize: configService.get('NODE_ENV') !== 'production',
                            ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
                        };
                    }
                    return {
                        type: 'postgres',
                        host: configService.get('AUTH_DB_HOST', 'localhost'),
                        port: configService.get('AUTH_DB_PORT', 5433),
                        username: configService.get('AUTH_DB_USERNAME', 'authuser'),
                        password: configService.get('AUTH_DB_PASSWORD', 'authpassword'),
                        database: configService.get('AUTH_DB_DATABASE', 'authdb'),
                        entities: [user_entity_1.User, refresh_token_entity_1.RefreshToken],
                        synchronize: configService.get('NODE_ENV') !== 'production',
                    };
                },
            }),
            auth_module_1.AuthModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map