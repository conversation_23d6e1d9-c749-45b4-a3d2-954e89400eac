{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,mEAA8D;AAGvD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACU,aAA4B,EAC5B,mBAAwC;QAEhD,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,iBAAiB,CAAC;SAChE,CAAC,CAAC;QAPK,kBAAa,GAAb,aAAa,CAAe;QAC5B,wBAAmB,GAAnB,mBAAmB,CAAqB;IAOlD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QAEzB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEnF,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,8BAAqB,CAAC,8BAA8B,CAAC,CAAC;QAClE,CAAC;QAED,OAAO;YACL,MAAM,EAAE,OAAO,CAAC,GAAG;YACnB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;SAClC,CAAC;IACJ,CAAC;CACF,CAAA;AA1BY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,sBAAa;QACP,2CAAmB;GAHvC,WAAW,CA0BvB"}