import { DataSource } from 'typeorm';
import { User } from './src/auth/user.entity';
import { RefreshToken } from './src/auth/refresh-token.entity';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: ['.env.local', '.env'] });

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.AUTH_DB_HOST || 'localhost',
  port: parseInt(process.env.AUTH_DB_PORT || '5433'),
  username: process.env.AUTH_DB_USERNAME || 'authuser',
  password: process.env.AUTH_DB_PASSWORD || 'authpassword',
  database: process.env.AUTH_DB_DATABASE || 'authdb',
  entities: [User, RefreshToken],
  migrations: ['src/migration/*.ts'],
  synchronize: false,
  logging: true,
});

export default AppDataSource;
