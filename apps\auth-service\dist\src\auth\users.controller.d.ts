import { UsersService } from './users.service';
import { UserDto } from './dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    resolveUsername(username: string): Promise<{
        userId: string;
    }>;
    getUserById(userId: string): Promise<UserDto>;
    getAllUsers(): Promise<UserDto[]>;
    createUser(createUserDto: {
        username: string;
        password: string;
        isAdmin?: boolean;
    }): Promise<UserDto>;
    updateUser(userId: string, updateUserDto: {
        username?: string;
        password?: string;
        isAdmin?: boolean;
    }): Promise<UserDto>;
    deleteUser(userId: string): Promise<{
        message: string;
    }>;
}
