import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { User } from './auth/user.entity';
import { RefreshToken } from './auth/refresh-token.entity';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        // Check if DATABASE_URL is provided (for production environments like Render)
        const databaseUrl = configService.get('AUTH_DATABASE_URL');
        
        if (databaseUrl) {
          return {
            type: 'postgres',
            url: databaseUrl,
            entities: [User, RefreshToken],
            synchronize: configService.get('NODE_ENV') !== 'production',
            ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
          };
        }

        // Use individual connection parameters if AUTH_DATABASE_URL is not provided
        return {
          type: 'postgres',
          host: configService.get('AUTH_DB_HOST', 'localhost'),
          port: configService.get('AUTH_DB_PORT', 5433),
          username: configService.get('AUTH_DB_USERNAME', 'authuser'),
          password: configService.get('AUTH_DB_PASSWORD', 'authpassword'),
          database: configService.get('AUTH_DB_DATABASE', 'authdb'),
          entities: [User, RefreshToken],
          synchronize: configService.get('NODE_ENV') !== 'production',
        };
      },
    }),
    AuthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
