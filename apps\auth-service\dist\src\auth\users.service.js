"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./user.entity");
let UsersService = class UsersService {
    constructor(userRepo) {
        this.userRepo = userRepo;
    }
    async findUserIdByUsername(username) {
        const user = await this.userRepo.findOne({ where: { username } });
        if (!user) {
            throw new common_1.NotFoundException(`User with username "${username}" not found`);
        }
        return user.id;
    }
    async findUsernameByUserId(userId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
        return user.username;
    }
    async findUserById(userId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
        const userDto = {
            id: user.id,
            username: user.username,
            isAdmin: user.isAdmin,
        };
        return userDto;
    }
    async getAllUsers() {
        const users = await this.userRepo.find();
        return users.map(user => ({
            id: user.id,
            username: user.username,
            isAdmin: user.isAdmin,
        }));
    }
    async createUser(username, hashedPassword, isAdmin = false) {
        const user = this.userRepo.create({
            username,
            password: hashedPassword,
            isAdmin,
        });
        const savedUser = await this.userRepo.save(user);
        return {
            id: savedUser.id,
            username: savedUser.username,
            isAdmin: savedUser.isAdmin,
        };
    }
    async updateUser(userId, updates) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
        Object.assign(user, updates);
        const updatedUser = await this.userRepo.save(user);
        return {
            id: updatedUser.id,
            username: updatedUser.username,
            isAdmin: updatedUser.isAdmin,
        };
    }
    async deleteUser(userId) {
        const result = await this.userRepo.delete(userId);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`User with ID "${userId}" not found`);
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UsersService);
//# sourceMappingURL=users.service.js.map