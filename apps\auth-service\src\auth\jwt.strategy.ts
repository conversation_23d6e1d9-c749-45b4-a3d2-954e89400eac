// auth/jwt.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { RefreshTokenService } from './refresh-token.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private refreshTokenService: RefreshTokenService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET', 'your_jwt_secret'),
    });
  }

  async validate(payload: any) {
    // Check if user has any valid refresh tokens (session is still active)
    const hasValidSession = await this.refreshTokenService.hasValidTokens(payload.sub);

    if (!hasValidSession) {
      throw new UnauthorizedException('Session has been invalidated');
    }

    return {
      userId: payload.sub,
      username: payload.username,
      isAdmin: payload.isAdmin || false
    };
  }
}
