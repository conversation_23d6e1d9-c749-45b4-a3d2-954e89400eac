import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { MessageService } from './message.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { Message } from './message.entity';
import { UsersService } from '../auth/users.service';
import { NotificationsService } from '../notifications/notifications.service';

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  },
})
@Injectable()
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(ChatGateway.name);

  // The only map for socket connections - using usernames only
  private usernameSocketMap = new Map<string, string>(); // username -> socketId

  constructor(
    private readonly jwtService: JwtService,
    private readonly messageService: MessageService,
    private readonly usersService: UsersService,
    private readonly notificationsService: NotificationsService,
  ) {}

  async handleConnection(socket: Socket) {
    console.log('Client connected:', socket.id);
    try {
      const token: string = socket.handshake.auth.token as string;
      if (!token) throw new UnauthorizedException('Token not provided');

      const payload = this.jwtService.verify(token);
      const username = payload.username;

      // Store socket mapping using username only
      this.usernameSocketMap.set(username, socket.id);

      // Store username in socket data
      socket.data.username = username;

      console.log(`User connected: ${username}`);

      // Note: Pending messages will be delivered when client sends 'client_ready' event
    } catch (err) {
      console.error('Connection error:', err.message);
      socket.disconnect();
    }
  }

  handleDisconnect(socket: Socket) {
    const username = socket.data.username;

    if (username) {
      console.log(`User disconnected: ${username}`);
      this.usernameSocketMap.delete(username);
    }
  }

  // NOTE: Socket-based offline message sync has been replaced with REST API
  // The client now fetches all messages via GET /api/messages when connecting
  // This eliminates the need for the client_ready event handler

  /**
   * Immediately invalidate a user's session by notifying them via Socket.IO
   * This is called when a user logs in from another device
   * Returns true if user was notified, false if user is offline
   */
  invalidateUserSession(username: string, reason: string = 'logged_in_elsewhere'): boolean {
    const socketId = this.usernameSocketMap.get(username);

    if (socketId) {
      console.log(`Invalidating session for user ${username} - reason: ${reason}`);

      // Emit session invalidation event to force immediate logout
      this.server.to(socketId).emit('session_invalidated', {
        reason,
        message: 'You have been logged out because you signed in from another device.',
        timestamp: new Date().toISOString()
      });

      // Disconnect the socket to prevent further communication
      const socket = this.server.sockets.sockets.get(socketId);
      if (socket) {
        socket.disconnect(true);
      }

      // Remove from our tracking map
      this.usernameSocketMap.delete(username);

      return true; // User was notified and disconnected
    } else {
      console.log(`User ${username} is not connected, cannot invalidate session`);
      return false; // User is offline
    }
  }

  /**
   * Notify a sender that their message has been delivered
   * This is called from the REST API when a user fetches their messages
   * Returns true if sender was notified, false if sender is offline
   */
  notifyMessageDelivered(
    senderUsername: string,
    messageId: string,
    receiverUsername: string,
    clientMessageId?: string | null
  ): boolean {
    const senderSocketId = this.usernameSocketMap.get(senderUsername);

    if (senderSocketId) {
      console.log(`Notifying sender ${senderUsername} about message delivery via REST API sync`);

      // Emit delivery confirmation to the sender
      this.server.to(senderSocketId).emit('message_delivered', {
        message_id: messageId,
        client_message_id: clientMessageId, // Use the original local message ID
        receiver_username: receiverUsername,
        status: 'delivered',
        delivery_method: 'rest_api_sync'
      });

      return true; // Sender was notified
    } else {
      console.log(`Sender ${senderUsername} is not connected, cannot notify about delivery`);
      return false; // Sender is offline
    }
  }

  @SubscribeMessage('message')
  handleMessage(
    @MessageBody() data: CreateMessageDto,
    @ConnectedSocket() socket: Socket
  ): { status: string, message: string } | void {
    console.log('Message received, type:', data.type);

    // Process the message asynchronously
    this.processMessage(data, socket).catch(error => {
      console.error('Error processing message:', error.message);
      socket.emit('error', { message: 'Failed to send message: ' + error.message });
    });

    // Only return acknowledgment for text messages
    if (data.type === 'text') {
      console.log('Returning acknowledgment for text message');
      return { status: 'acknowledged', message: 'Message received by server' };
    }

    // For non-text messages, don't return an acknowledgment
    console.log('No acknowledgment for non-text message type:', data.type);
    return;
  }

  // We're now using Socket.IO acknowledgments for delivery confirmation
  // The separate message_delivered handler is no longer needed

  @SubscribeMessage('delivery_confirmed')
  async handleDeliveryConfirmed(
    @MessageBody() data: { message_id: string },
    @ConnectedSocket() socket: Socket
  ) {
    try {
      const username = socket.data.username;
      if (!username) {
        throw new UnauthorizedException('User not authenticated');
      }

      console.log(`Delivery confirmation received from ${username} for message ${data.message_id}`);

      // Delete the delivered message from the backend database
      try {
        await this.messageService.delete(data.message_id);
        console.log(`Message ${data.message_id} deleted from backend after delivery confirmation`);
      } catch (deleteError) {
        console.error(`Failed to delete message ${data.message_id} after delivery confirmation:`, deleteError);
      }
    } catch (error) {
      console.error('Error handling delivery confirmation:', error.message);
      socket.emit('error', { message: 'Failed to process delivery confirmation: ' + error.message });
    }
  }

  private async processMessage(data: CreateMessageDto, socket: Socket) {
    try {
      const senderUsername = socket.data.username;
      if (!senderUsername) {
        throw new UnauthorizedException('User not authenticated');
      }

      // Get the receiver username directly from the data
      const receiverUsername = data.receiver_username; // Using the proper field name

      // Create the message with usernames only
      const message = {
        ...data,
        sender_username: senderUsername,
        type: data.type || 'text',
      };

      // Verify that the receiver username exists
      try {
        await this.usersService.findUserIdByUsername(receiverUsername);
      } catch (error) {
        throw new NotFoundException(`User with username "${receiverUsername}" not found`);
      }

      // Try to deliver the message by username
      const receiverSocketId = this.usernameSocketMap.get(receiverUsername);

      // Only save text messages to the database, not typing indicators or other types
      if (message.type === 'text') {
        // Save the message to the database
        const saved = await this.messageService.savePendingMessage(message);

        // If we found a socket connection for the receiver, deliver the message
        if (receiverSocketId) {
          console.log(`Delivering text message to ${receiverUsername} (ID: ${saved.id})`);

          // First emit the message with acknowledgment callback
          this.server.to(receiverSocketId).emit('message', {
            ...message,
            id: saved.id, // Make sure to include the message ID
            client_message_id: message.client_message_id // Include the client's message ID if available
          }, (deliveryAck: any) => {
            // Log the acknowledgment for debugging
            console.log('Delivery acknowledgment received:');

            // Check if deliveryAck exists and has the expected structure
            if (deliveryAck && typeof deliveryAck === 'object') {
              console.log(`Message ${saved.id} received acknowledgment from ${receiverUsername}`);

              // Find the sender's socket to notify them about the delivery
              const senderSocketId = this.usernameSocketMap.get(senderUsername);

              if (senderSocketId) {
                console.log(`Notifying sender ${senderUsername} about message delivery`);

                // Emit delivery confirmation to the sender
                this.server.to(senderSocketId).emit('message_delivered', {
                  message_id: saved.id,
                  client_message_id: message.client_message_id, // Include the client's message ID if available
                  receiver_username: receiverUsername,
                  status: 'delivered'
                });
              }
            } else {
              console.log(`Message ${saved.id} received invalid acknowledgment from ${receiverUsername}:`, deliveryAck);
            }
          });

          // Then delete the message from pending messages
          try {
            await this.messageService.delete(saved.id);
            console.log(`Message ${saved.id} deleted from pending messages after delivery`);
          } catch (deleteError) {
            console.error(`Failed to delete message ${saved.id} after delivery:`, deleteError);
          }
        } else {
          this.logger.log(`User ${receiverUsername} is offline, text message saved for later delivery (ID: ${saved.id})`);

          // Send push notification since user is offline
          try {
            await this.notificationsService.sendNotification(
              receiverUsername,
              `New message from ${senderUsername}`,
              message.message.length > 100 ? message.message.substring(0, 97) + '...' : message.message,
              {
                type: 'new_message',
                sender: senderUsername,
                messageId: saved.id,
                timestamp: Date.now().toString(),
              }
            );
            this.logger.log(`Push notification sent to offline user ${receiverUsername}`);
          } catch (notificationError) {
            this.logger.error(`Failed to send push notification to ${receiverUsername}:`, notificationError);
          }
        }
      } else {
        // For non-text messages (typing indicators, etc.), just emit them without saving
        if (receiverSocketId) {
          console.log(`Delivering ${message.type} message to ${receiverUsername} (not saved)`);
          this.server.to(receiverSocketId).emit('message', message);
        }
      }
    } catch (error) {
      console.error('Error handling message:', error.message);
      throw error;
    }
  }
}
