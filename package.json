{"name": "chatspot", "version": "1.0.0", "description": "ChatSpot Messenger - A full-stack chat application with web and mobile clients", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install-all": "yarn install", "backend": "yarn workspace chatspot-backend start:dev", "backend:localhost": "yarn workspace chatspot-backend start:localhost", "auth-service": "yarn workspace auth-service start:dev", "auth-service:localhost": "yarn workspace auth-service start:localhost", "frontend": "yarn workspace chatspot-frontend dev", "admin": "yarn workspace chatspot-admin dev", "mobile": "yarn workspace mobile start", "mobile:localhost": "yarn workspace mobile start:localhost", "dev:localhost": "concurrently \"yarn run auth-service:localhost\" \"yarn run backend:localhost\" \"yarn run mobile:localhost\"", "dev:web": "concurrently \"yarn run auth-service\" \"yarn run backend\" \"yarn run frontend\"", "dev:admin": "concurrently \"yarn run auth-service\" \"yarn run backend\" \"yarn run admin\"", "dev:microservices": "concurrently \"yarn run auth-service\" \"yarn run backend\"", "test:backend": "yarn workspace chatspot-backend test", "test:auth-service": "yarn workspace auth-service test", "test:frontend": "yarn workspace chatspot-frontend test", "test:mobile": "yarn workspace mobile test", "test:all": "concurrently \"yarn run test:backend\" \"yarn run test:auth-service\" \"yarn run test:frontend\" \"yarn run test:mobile\"", "build:backend": "yarn workspace chatspot-backend build", "build:auth-service": "yarn workspace auth-service build", "build:frontend": "yarn workspace chatspot-frontend build", "build:admin": "yarn workspace chatspot-admin build", "build:all": "yarn run build:backend && yarn run build:auth-service && yarn run build:frontend && yarn run build:admin", "clean": "rimraf node_modules && rimraf apps/*/node_modules && rimraf packages/*/node_modules", "create-admin": "yarn workspace auth-service create-admin", "reset-db": "yarn workspace chatspot-backend reset-db", "reset-auth-db": "yarn workspace auth-service reset-db"}, "keywords": ["chat", "messenger", "react", "react-native", "<PERSON><PERSON><PERSON>", "monorepo"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}